<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { 
  ElDialog, ElCard, ElFormItem, ElDatePicker, ElTag, ElIcon, ElButton, 
  ElSelect, ElOption, ElInput, ElRow, ElCol, ElMessage, ElMessageBox 
} from 'element-plus';
import { InfoFilled, Plus, Delete, Check, Clock } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { TimeSlot } from '@/types/afterSales/quota.d.ts';

interface Props {
  visible: boolean;
  isEdit: boolean;
  selectedDate: string;
  timeSlots: TimeSlot[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'update:selectedDate', value: string): void;
  (e: 'update:timeSlots', value: TimeSlot[]): void;
  (e: 'save', data: { date: string; timeSlots: Omit<TimeSlot, 'id'>[] }): void;
  (e: 'date-change', date: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

const { t, tc } = useModuleI18n('afterSales.quota');

const slotIdCounter = ref(0);

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const modalSelectedDate = computed({
  get: () => props.selectedDate,
  set: (value) => emit('update:selectedDate', value)
});

const modalTimeSlots = computed({
  get: () => props.timeSlots,
  set: (value) => emit('update:timeSlots', value)
});

// 检查是否有已有配置
const hasExistingConfig = computed(() => {
  return props.timeSlots.length > 0 && props.isEdit;
});

// 汇总信息
const summaryInfo = computed(() => {
  if (!modalSelectedDate.value) {
    return t('summary.selectDate');
  }
  if (modalTimeSlots.value.length === 0) {
    return t('summary.addTimeSlot');
  }
  const totalQuota = modalTimeSlots.value.reduce((sum, slot) => sum + slot.quota, 0);
  return `${modalSelectedDate.value} · ${modalTimeSlots.value.length}${t('summary.timeSlotsUnit')} · ${t('summary.totalQuota')}${totalQuota}`;
});

// 日期变更处理
const onDateChange = () => {
  if (modalSelectedDate.value) {
    const selectedDate = new Date(modalSelectedDate.value);
    const today = new Date(new Date().setHours(0, 0, 0, 0));
    if (selectedDate < today) {
      ElMessage.warning(t('messages.dateMustBeFutureOrToday'));
      modalSelectedDate.value = today.toISOString().split('T')[0];
      return;
    }
    emit('date-change', modalSelectedDate.value);
  }
};

// 添加时段
const addTimeSlot = () => {
  if (!modalSelectedDate.value) {
    ElMessage.warning(t('messages.selectDateFirst'));
    return;
  }

  const newSlot: TimeSlot = {
    id: slotIdCounter.value++,
    start: '09:00',
    end: '10:00',
    quota: 1,
  };

  modalTimeSlots.value = [...modalTimeSlots.value, newSlot];
};

// 删除时段
const deleteTimeSlot = (slotId: number) => {
  modalTimeSlots.value = modalTimeSlots.value.filter(slot => slot.id !== slotId);
};

// 更新时段
const updateTimeSlot = (slotId: number, field: 'start' | 'end' | 'quota', value: string | number) => {
  const slots = [...modalTimeSlots.value];
  const slotIndex = slots.findIndex(slot => slot.id === slotId);
  
  if (slotIndex !== -1) {
    if (field === 'quota') {
      slots[slotIndex].quota = Math.max(1, Math.floor(Number(value)));
    } else {
      slots[slotIndex][field] = value as string;
    }
    
    // 如果修改开始时间，确保结束时间仍然有效
    if (field === 'start') {
      const startTime = slots[slotIndex].start;
      const endTime = slots[slotIndex].end;
      if (compareTimes(startTime, endTime) >= 0) {
        const [startHour, startMin] = startTime.split(':').map(Number);
        const newEndTime = new Date();
        newEndTime.setHours(startHour + 1, startMin, 0);
        slots[slotIndex].end = `${String(newEndTime.getHours()).padStart(2, '0')}:${String(newEndTime.getMinutes()).padStart(2, '0')}`;
      }
    }
    
    modalTimeSlots.value = slots;
  }
};

// 生成时间选项
const getTimeOptions = (isEndTime: boolean, startTime?: string) => {
  const options = [];
  for (let h = 8; h <= 18; h++) {
    for (const m of [0, 30]) {
      if (h === 18 && m > 0) continue;
      const time = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}`;
      if (isEndTime && startTime) {
        if (compareTimes(time, startTime) > 0) {
          options.push({ label: time, value: time });
        }
      } else {
        options.push({ label: time, value: time });
      }
    }
  }
  return options;
};

// 比较时间
const compareTimes = (time1: string, time2: string) => {
  const [h1, m1] = time1.split(':').map(Number);
  const [h2, m2] = time2.split(':').map(Number);
  if (h1 !== h2) return h1 - h2;
  return m1 - m2;
};

// 校验配置
const validateConfig = (): boolean => {
  if (!modalSelectedDate.value) {
    ElMessage.error(t('validation.dateRequired'));
    return false;
  }
  if (modalTimeSlots.value.length === 0) {
    ElMessage.error(t('validation.atLeastOneTimeSlot'));
    return false;
  }

  for (const slot of modalTimeSlots.value) {
    if (!slot.start || !slot.end) {
      ElMessage.error(t('validation.timeRequired'));
      return false;
    }
    if (compareTimes(slot.start, slot.end) >= 0) {
      ElMessage.error(t('validation.startBeforeEnd'));
      return false;
    }
    if (slot.quota < 1) {
      ElMessage.error(t('validation.quotaPositive'));
      return false;
    }
  }
  return true;
};

// 保存配置
const saveConfig = () => {
  if (!validateConfig()) {
    return;
  }

  const saveData = {
    date: modalSelectedDate.value,
    timeSlots: modalTimeSlots.value.map(slot => ({
      start: slot.start,
      end: slot.end,
      quota: slot.quota,
    })),
  };

  emit('save', saveData);
};

// 关闭弹窗
const closeDialog = async () => {
  if (modalTimeSlots.value.length > 0) {
    try {
      await ElMessageBox.confirm(
        t('messages.unsavedChangesWarning'),
        tc('warning'), {
        confirmButtonText: tc('confirm'),
        cancelButtonText: tc('cancel'),
        type: 'warning',
      });
      dialogVisible.value = false;
    } catch {
      // 用户取消关闭
    }
  } else {
    dialogVisible.value = false;
  }
};

// 监听时段变化，更新计数器
watch(() => props.timeSlots, (newSlots) => {
  if (newSlots.length > 0) {
    slotIdCounter.value = Math.max(...newSlots.map(s => s.id)) + 1;
  }
}, { immediate: true });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? t('modal.editTitle') : t('modal.title')"
    width="800px"
    class="quota-dialog"
    :before-close="closeDialog"
  >
    <div class="dialog-content">
      <!-- 日期选择区 -->
      <el-card class="mb-20">
        <template #header>
          <span>{{ t('modal.selectDate') }}</span>
        </template>
        <el-form-item :label="t('modal.dateLabel')">
          <el-date-picker
            v-model="modalSelectedDate"
            type="date"
            value-format="YYYY-MM-DD"
            :placeholder="t('modal.datePlaceholder')"
            style="width: 100%;"
            :clearable="false"
            :disabled-date="(date: Date) => date.getTime() < new Date(new Date().setHours(0,0,0,0)).getTime()"
            @change="onDateChange"
          />
        </el-form-item>
        <div v-if="hasExistingConfig" class="existing-config-tip">
          <el-tag type="warning">{{ t('modal.existingConfig') }}</el-tag>
        </div>
        <div class="info-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>{{ t('modal.dateTip') }}</span>
        </div>
      </el-card>

      <!-- 时段配置区 -->
      <el-card class="mb-20">
        <template #header>
          <div class="card-header-flex">
            <span>{{ t('modal.timeSlotConfiguration') }}</span>
            <el-button type="info" :icon="Plus" link @click="addTimeSlot">
              {{ t('modal.addTimeSlot') }}
            </el-button>
          </div>
        </template>

        <div v-if="modalTimeSlots.length === 0" class="empty-state-modal">
          <el-icon class="empty-icon"><Clock /></el-icon>
          <p>{{ t('modal.noTimeSlots') }}</p>
          <p>{{ t('modal.clickAddPrompt') }}</p>
        </div>

        <div class="time-slot-list">
          <el-card v-for="(slot, index) in modalTimeSlots" :key="slot.id" class="time-slot-card mb-10">
            <template #header>
              <div class="card-header-flex">
                <span>{{ t('modal.timeSlot') }} {{ index + 1 }}</span>
                <el-button type="danger" :icon="Delete" link @click="deleteTimeSlot(slot.id)">
                  {{ tc('delete') }}
                </el-button>
              </div>
            </template>
            <el-row :gutter="10" align="middle">
              <el-col :xs="24" :sm="12" :md="8">
                <el-form-item :label="t('modal.startTime')">
                  <el-select
                    v-model="slot.start"
                    :placeholder="t('modal.startTimePlaceholder')"
                    class="time-select"
                    @change="(val: string) => updateTimeSlot(slot.id, 'start', val)"
                  >
                    <el-option
                      v-for="item in getTimeOptions(false)"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8">
                <el-form-item :label="t('modal.endTime')">
                  <el-select
                    v-model="slot.end"
                    :placeholder="t('modal.endTimePlaceholder')"
                    class="time-select"
                    @change="(val: string) => updateTimeSlot(slot.id, 'end', val)"
                  >
                    <el-option
                      v-for="item in getTimeOptions(true, slot.start)"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="8">
                <el-form-item :label="t('modal.quota')">
                  <el-input
                    v-model.number="slot.quota"
                    type="number"
                    :min="1"
                    :placeholder="t('modal.quotaPlaceholder')"
                    @change="(val: string | number) => updateTimeSlot(slot.id, 'quota', val)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-card>

      <!-- 配置说明区 -->
      <el-card>
        <template #header>
          <span>{{ t('modal.configDescriptionTitle') }}</span>
        </template>
        <ul>
          <li>{{ t('modal.configDescription.item1') }}</li>
          <li>{{ t('modal.configDescription.item2') }}</li>
          <li>{{ t('modal.configDescription.item3') }}</li>
          <li>{{ t('modal.configDescription.item4') }}</li>
        </ul>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer-buttons">
        <span class="summary-info">{{ summaryInfo }}</span>
        <div>
          <el-button @click="closeDialog">{{ tc('cancel') }}</el-button>
          <el-button type="primary" :icon="Check" @click="saveConfig" :loading="loading">
            {{ tc('save') }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.quota-dialog {
  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
  }

  .mb-20 {
    margin-bottom: 20px;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .card-header-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  .existing-config-tip {
    margin-top: 10px;
    text-align: right;
  }

  .info-tip {
    font-size: 13px;
    color: #909399;
    margin-top: 10px;
    .el-icon {
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .empty-state-modal {
    text-align: center;
    padding: 30px 0;
    color: #909399;
    .empty-icon {
      font-size: 50px;
      margin-bottom: 10px;
    }
    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }

  .time-slot-list {
    .time-slot-card {
      background-color: #F8F8F8;
      border: 1px dashed #DCDFE6;
      .el-form-item {
        margin-bottom: 0;
      }
      .time-select {
        min-width: 120px;
        width: 100%;
      }
    }
  }

  .dialog-footer-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .summary-info {
      font-weight: bold;
      color: #303133;
      font-size: 16px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
