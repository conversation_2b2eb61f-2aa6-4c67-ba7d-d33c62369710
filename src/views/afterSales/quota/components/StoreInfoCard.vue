<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from 'element-plus';
import { InfoFilled } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { StoreInfo } from '@/types/afterSales/quota.d.ts';

interface Props {
  storeInfo: StoreInfo;
}

defineProps<Props>();

const { t } = useModuleI18n('afterSales.quota');
</script>

<template>
  <el-card class="store-info-card">
    <el-row :gutter="20" class="store-info-row">
      <el-col :span="12">
        <span>{{ t('storeName') }}：{{ storeInfo.name }}</span>
      </el-col>
      <el-col :span="12">
        <span>{{ t('storeCode') }}：{{ storeInfo.code }}</span>
      </el-col>
    </el-row>
    <el-row class="permission-tip">
      <el-col>
        <el-icon><InfoFilled /></el-icon>
        <span>{{ t('permissionTip') }}</span>
      </el-col>
    </el-row>
  </el-card>
</template>

<style scoped lang="scss">
.store-info-card {
  .store-info-row {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
    span {
      display: inline-block;
      margin-right: 20px;
    }
  }
  .permission-tip {
    font-size: 14px;
    color: #909399;
    .el-icon {
      vertical-align: middle;
      margin-right: 5px;
    }
  }
}
</style>
