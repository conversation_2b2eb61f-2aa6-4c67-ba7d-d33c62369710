<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox, ElCard, ElButton, ElRow, ElCol } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import { 
  getCheckinList, 
  addCheckinRecord, 
  updateCheckinRecord, 
  deleteCheckinRecord, 
  createRelatedRepairOrder 
} from '@/api/modules/afterSales/checkin';
import type { 
  CheckinListItem, 
  CheckinListParams, 
  CheckinFormData 
} from '@/types/afterSales/checkin.d.ts';

// 导入子组件
import CheckinSearchForm from './components/CheckinSearchForm.vue';
import CheckinTable from './components/CheckinTable.vue';
import CheckinFormDialog from './components/CheckinFormDialog.vue';
import CheckinDetailDialog from './components/CheckinDetailDialog.vue';

const { t, tc } = useModuleI18n('afterSales.checkin');

// 搜索相关
const searchParams = reactive<CheckinListParams>({
  checkinId: '',
  licensePlate: '',
  repairPersonName: '',
  repairPersonPhone: '',
  createdAtStart: '',
  createdAtEnd: '',
});
const dateRange = ref<[string, string] | null>(null);

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchParams.createdAtStart = newVal[0];
    searchParams.createdAtEnd = newVal[1];
  } else {
    searchParams.createdAtStart = '';
    searchParams.createdAtEnd = '';
  }
});

const checkinList = ref<CheckinListItem[]>([]);
const loading = ref(false);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 弹窗相关
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const isEdit = ref(false);
const currentRecord = ref<CheckinListItem | null>(null);

// 获取到店登记列表
const fetchCheckinList = async () => {
  loading.value = true;
  try {
    const response = await getCheckinList({
      ...searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    });
    checkinList.value = response.list;
    pagination.total = response.total;
  } catch (error) {
    console.error('Failed to fetch checkin list:', error);
    ElMessage.error(tc('operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchCheckinList();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    checkinId: '',
    licensePlate: '',
    repairPersonName: '',
    repairPersonPhone: '',
    createdAtStart: '',
    createdAtEnd: '',
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchCheckinList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchCheckinList();
};

const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchCheckinList();
};

// 新增登记
const handleAddCheckinRecord = () => {
  isEdit.value = false;
  currentRecord.value = null;
  formDialogVisible.value = true;
};

// 编辑登记
const handleEditRecord = (row: CheckinListItem) => {
  isEdit.value = true;
  currentRecord.value = row;
  formDialogVisible.value = true;
};

// 删除登记
const handleDeleteRecord = async (row: CheckinListItem) => {
  ElMessageBox.confirm(
    tc('confirmDelete', { item: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await deleteCheckinRecord(row.checkinId);
        ElMessage.success(tc('operationSuccessful'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to delete checkin record:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 创建维修单
const handleCreateRepairOrder = async (row: CheckinListItem) => {
  if (row.relatedRepairOrderId) {
    ElMessage.warning(t('repairOrderAlreadyExists'));
    return;
  }
  ElMessageBox.confirm(
    t('confirmCreateRepairOrder', { checkinId: row.checkinId }),
    tc('warning'),
    {
      confirmButtonText: tc('confirm'),
      cancelButtonText: tc('cancel'),
      type: 'info',
    }
  )
    .then(async () => {
      try {
        await createRelatedRepairOrder(row.checkinId);
        ElMessage.success(t('repairOrderCreatedSuccess'));
        fetchCheckinList();
      } catch (error) {
        console.error('Failed to create repair order:', error);
        ElMessage.error(tc('operationFailed'));
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// 查看详情
const handleViewDetails = (row: CheckinListItem) => {
  currentRecord.value = row;
  detailDialogVisible.value = true;
};

// 表单提交
const handleFormSubmit = async (formData: CheckinFormData) => {
  try {
    if (isEdit.value && currentRecord.value) {
      await updateCheckinRecord(currentRecord.value.checkinId, formData);
    } else {
      await addCheckinRecord(formData);
    }
    ElMessage.success(tc('operationSuccessful'));
    formDialogVisible.value = false;
    fetchCheckinList();
  } catch (error) {
    console.error('Failed to save checkin record:', error);
    ElMessage.error(tc('operationFailed'));
  }
};

// 导出
const handleExport = () => {
  ElMessage.info(tc('exportingReport'));
  console.log('Exporting data with params:', searchParams);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchCheckinList();
});
</script>

<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('checkinList') }}</h1>

    <!-- 搜索表单 -->
    <CheckinSearchForm
      v-model:search-params="searchParams"
      v-model:date-range="dateRange"
      @search="handleSearch"
      @reset="resetSearch"
    />

    <!-- 操作区域 -->
    <el-card class="mb-20 operation-card">
      <el-row :gutter="20" justify="end">
        <el-col :span="24" style="text-align: right;">
          <el-button type="primary" :icon="Plus" @click="handleAddCheckinRecord">
            {{ t('createRecord') }}
          </el-button>
          <el-button :icon="Download" @click="handleExport">
            {{ t('export') }}
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <CheckinTable
      :checkin-list="checkinList"
      :loading="loading"
      :pagination="pagination"
      @view-details="handleViewDetails"
      @edit-record="handleEditRecord"
      @delete-record="handleDeleteRecord"
      @create-repair-order="handleCreateRepairOrder"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- 新增/编辑弹窗 -->
    <CheckinFormDialog
      v-model:visible="formDialogVisible"
      :is-edit="isEdit"
      :record-data="currentRecord"
      @submit="handleFormSubmit"
    />

    <!-- 详情弹窗 -->
    <CheckinDetailDialog
      v-model:visible="detailDialogVisible"
      :record-data="currentRecord"
    />
  </div>
</template>

<style scoped lang="scss">
.page-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.operation-card {
  margin-bottom: 20px;
}
</style>
