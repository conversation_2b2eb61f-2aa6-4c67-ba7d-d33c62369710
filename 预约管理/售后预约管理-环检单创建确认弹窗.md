# 售后预约管理-环检单创建确认弹窗设计文档

## 页面概述

### 页面定位
环检单创建确认弹窗是预约管理系统中的关键操作组件，以模态弹窗形式为服务顾问提供环检单创建前的信息确认和编辑功能，确保信息准确性并启动正式服务流程。

### 设计目标
- **信息确认**：展示完整预约信息供服务顾问确认
- **信息编辑**：允许编辑送修人相关信息
- **操作明确**：提供清晰的确认和取消操作
- **流程控制**：确保只有符合条件的预约才能创建环检单

### 触发条件
- 预约状态必须为"已到店"
- 预约未创建过环检单
- 用户具有创建环检单的权限
- 点击预约列表中的"创建环检单"按钮

## 弹窗整体设计

### 弹窗规格
- **宽度**：600px（固定宽度）
- **高度**：自适应内容，最大高度80vh
- **位置**：屏幕居中显示
- **遮罩**：半透明黑色背景，透明度0.5
- **圆角**：8px圆角设计
- **阴影**：0 8px 24px rgba(0,0,0,0.15)

### 弹窗结构
```
┌─────────────────────────────────────────────────────────────┐
│ 弹窗头部区域 (Header) - 高度60px                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 弹窗内容区域 (Content) - 自适应高度                          │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ 弹窗底部区域 (Footer) - 高度72px                              │
└─────────────────────────────────────────────────────────────┘
```

## 弹窗头部设计

### 布局结构
- **高度**：60px
- **内边距**：0 24px
- **背景色**：白色 (#FFFFFF)
- **边框**：底部1px实线 #F0F0F0

### 内容组成
```
┌─────────────────────────────────────────────────┬─────────────┐
│ 🔧 创建环检单                                    │      ×      │
│ (18px字体，字重600，颜色#333333)                  │  (关闭按钮)  │
└─────────────────────────────────────────────────┴─────────────┘
```

### 设计规范
- **标题**：包含工具图标，突出操作性质
- **关闭按钮**：24x24px图标，支持ESC键快捷关闭
- **颜色主题**：绿色主题，与创建操作语义一致

## 弹窗内容设计

### 内容布局
- **内边距**：24px
- **背景色**：白色 (#FFFFFF)
- **滚动**：内容超出时垂直滚动
- **分段间距**：信息段之间20px间距

### 信息展示和编辑区域

#### 1. 预约确认信息段
```
┌─────────────────────────────────────────────────────────────┐
│ 📋 预约信息确认                                              │
│ ─────────────────────────────────────────────────────────── │
│ 预约单号：AP202411250001                                     │
│ 车牌号：京A12345                                             │
│ 车型：BMW X5 2.0T豪华版                                     │
│ 预约时间：2024-11-25 09:00-10:00                           │
│ 服务类型：[保养]                                             │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- **只读展示**：所有信息为只读，无法编辑
- **重点突出**：车牌号使用较大字体和加粗显示
- **颜色编码**：服务类型使用彩色标签显示
- **信息精简**：只显示关键识别信息

#### 2. 服务内容确认段 (仅当服务类型为“保养”时显示)
```
┌─────────────────────────────────────────────────────────────┐
│ 🔧 服务内容确认 (保养套餐)                                   │
│ ─────────────────────────────────────────────────────────── │
│ **套餐名称：** 基础保养A (PKG001)                            │
│ **预估总价：** ¥800                                          │
│                                                             │
│ **工时明细 (Labor Details)**                                │
│ ┌──────────────┬──────────┬──────────┬──────┬───────────┐    │
│ │ 项目名称     │ 项目编码 │ 标准工时 │ 单价 │ 小计      │    │
│ ├──────────────┼──────────┼──────────┼──────┼───────────┤    │
│ │ 更换机油机滤 │ L001     │ 1.0h     │ ¥200 │ ¥200      │    │
│ │ 全车检查     │ L002     │ 0.5h     │ ¥100 │ ¥50       │    │
│ └──────────────┴──────────┴──────────┴──────┴───────────┘    │
│                                                             │
│ **零件明细 (Parts Details)**                                │
│ ┌──────────────┬──────────┬──────┬──────┬───────────┐    │
│ │ 零件名称     │ 零件编码 │ 数量 │ 单价 │ 小计      │    │
│ ├──────────────┼──────────┼──────┼──────┼───────────┤    │
│ │ 宝马原厂机油 │ P001     │ 5L   │ ¥80  │ ¥400      │    │
│ │ 原厂机油滤芯 │ P002     │ 1个  │ ¥150 │ ¥150      │    │
│ └──────────────┴──────────┴──────┴──────┴───────────┘    │
└─────────────────────────────────────────────────────────────┘
```
**设计规范**：
- **条件显示**：仅当服务类型为"保养"时显示此整个区块。
- **表格展示**：使用清晰的表格（Markdown table format）展示工时和零件明细。
- **信息完整**：确保Code、名称、数量、单位都完整显示。

#### 3. 支付信息确认段
```
┌─────────────────────────────────────────────────────────────┐
│ 💳 支付信息确认                                              │
│ ─────────────────────────────────────────────────────────── │
│ 支付方式：[线上支付]                                         │
│ 支付状态：[已支付]                                           │
└─────────────────────────────────────────────────────────────┘
```
**设计规范**：
- **标签显示**：支付方式和状态使用标签或徽章样式，便于快速识别。
- **状态颜色**：支付状态根据实际情况（如已支付-绿色，未支付-橙色）使用不同颜色。

#### 4. 客户信息确认段
```
┌─────────────────────────────────────────────────────────────┐
│ 👤 客户信息确认                                              │
│ ─────────────────────────────────────────────────────────── │
│ 预约下单人：张三                                             │
│ 预约人手机：138****1234                                      │
│                                                             │
│ 📝 送修人信息（可编辑）                                      │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 送修人姓名 *                                            │ │
│ │ [李四                    ]                              │ │
│ │                                                         │ │
│ │ 送修人手机 *                                            │ │
│ │ [13900000000             ]                              │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- **预约人信息**：只读显示，手机号脱敏处理
- **送修人信息**：白色背景编辑区域，边框突出
- **必填标识**：字段名后加红色星号(*)标识
- **输入框样式**：40px高度，边框1px #D9D9D9，获焦点时边框变蓝
- **输入验证**：手机号格式实时验证

#### 3. 操作说明段
```
┌─────────────────────────────────────────────────────────────┐
│ ℹ️ 操作说明                                                 │
│ ─────────────────────────────────────────────────────────── │
│ • 请确认以上信息是否正确，可编辑送修人信息                    │
│ • 创建环检单后将自动分配服务顾问并开始检测流程                │
│ • 环检单创建后无法撤销，请谨慎操作                            │
└─────────────────────────────────────────────────────────────┘
```

**设计规范**：
- **信息图标**：使用信息图标突出说明性质
- **要点列表**：使用项目符号列表清晰展示要点
- **警告提示**：重要操作说明使用橙色文字突出
- **字体大小**：13px字体，颜色#666666

## 弹窗底部设计

### 布局结构
- **高度**：72px
- **内边距**：20px 24px
- **背景色**：#FAFAFA
- **边框**：顶部1px实线 #F0F0F0

### 按钮布局
```
┌─────────────────────────────────────────┬─────────┬─────────────┐
│                                         │ [取消]  │ [确认创建]  │
│                                         │         │             │
└─────────────────────────────────────────┴─────────┴─────────────┘
```

### 按钮设计规范

#### 取消按钮
- **样式**：次要按钮（灰色主题）
- **尺寸**：高度40px，宽度80px
- **文字**："取消"
- **功能**：关闭弹窗，不执行创建操作

#### 确认创建按钮
- **样式**：主要按钮（绿色主题）
- **尺寸**：高度40px，宽度120px
- **文字**："确认创建"
- **图标**：加号图标
- **状态管理**：
  - 默认状态：绿色可点击
  - 加载状态：显示loading图标，禁用点击
  - 禁用状态：必填信息未填写时灰色禁用

## 交互行为设计

### 1. 弹窗打开
- **触发条件**：
  - 预约状态为"已到店"
  - 用户点击"创建环检单"按钮
  - 权限验证通过
- **加载数据**：获取预约详细信息
- **初始化**：自动填充送修人信息（默认同预约人）
- **焦点设置**：送修人姓名输入框获得焦点

### 2. 信息编辑交互

#### 送修人姓名输入
- **输入限制**：最大20个字符
- **验证规则**：不能为空，不能包含特殊字符
- **实时反馈**：输入时实时计算字符数
- **错误提示**：格式错误时显示红色错误信息

#### 送修人手机输入
- **输入限制**：11位数字
- **验证规则**：必须1开头的11位手机号
- **格式化**：输入时自动格式化为3-4-4格式显示
- **验证反馈**：实时验证并显示验证状态

### 3. 表单验证

#### 验证时机
- **实时验证**：输入时立即验证格式
- **失焦验证**：输入框失去焦点时验证完整性
- **提交验证**：点击确认创建时最终验证

#### 验证规则
```javascript
// 送修人姓名验证
姓名验证规则：{
  必填: true,
  最小长度: 2,
  最大长度: 20,
  格式: "仅支持中文、英文和数字",
  禁止字符: ["<", ">", "&", "'", "\""]
}

// 送修人手机验证
手机验证规则：{
  必填: true,
  格式: "11位数字",
  正则: /^1[3-9]\d{9}$/,
  错误提示: "请输入正确的手机号格式"
}
```

#### 错误显示
- **错误位置**：输入框下方显示错误信息
- **错误样式**：红色文字，14px字体
- **错误图标**：感叹号图标配合错误文字
- **动态更新**：验证通过后自动清除错误信息

### 4. 创建操作流程

#### 创建前检查
1. **表单验证**：检查所有必填字段格式正确
2. **权限确认**：再次确认用户操作权限
3. **数据准备**：组装创建环检单所需数据
4. **状态切换**：按钮切换为加载状态

#### 创建执行
1. **loading状态**：按钮显示loading图标和"创建中..."文字
2. **禁用交互**：禁用所有输入和按钮
3. **进度提示**：显示创建进度提示
4. **超时处理**：超过10秒显示超时提示

#### 创建结果处理
**成功场景：**
1. 显示成功提示："环检单创建成功"
2. 更新预约状态显示
3. 自动关闭弹窗（1秒后）
4. 刷新预约列表显示最新状态
5. 可选择跳转到环检单详情页面

**失败场景：**
1. 显示具体错误信息
2. 按钮恢复正常状态
3. 允许用户重新尝试
4. 记录错误日志用于排查

## 数据流设计

### 1. 数据输入
```javascript
输入数据结构：{
  预约单号: "AP202411250001",
  车牌号: "京A12345",
  车型信息: "BMW X5 2.0T豪华版",
  预约时间: "2024-11-25 09:00-10:00",
  服务类型: "保养",
  预约下单人: "张三",
  预约人手机: "13800000000",
  送修人姓名: "李四",  // 可编辑
  送修人手机: "13900000000"  // 可编辑
}
```

### 2. 数据输出
```javascript
创建环检单请求数据：{
  appointmentId: "AP202411250001",
  serviceContactName: "李四",
  serviceContactPhone: "13900000000",
  operatorId: "当前登录用户ID",
  createTime: "2024-11-25 09:10:35"
}
```

### 3. 数据验证
- **前端验证**：格式验证、必填验证
- **后端验证**：业务规则验证、权限验证
- **双重保障**：确保数据安全和业务正确性

## 状态管理

### 1. 弹窗状态
- **未加载**：初始状态，显示loading
- **加载中**：获取预约数据中
- **已加载**：数据加载完成，可编辑
- **验证中**：表单验证进行中
- **创建中**：环检单创建进行中
- **创建成功**：显示成功状态
- **创建失败**：显示失败状态

### 2. 表单状态
- **编辑状态**：用户可以输入编辑
- **验证状态**：实时验证显示结果
- **禁用状态**：创建过程中禁用编辑
- **错误状态**：验证失败显示错误

### 3. 按钮状态
- **正常状态**：可点击，绿色主题
- **禁用状态**：表单验证未通过时灰色禁用
- **加载状态**：创建中显示loading
- **成功状态**：短暂显示成功图标

## 错误处理设计

### 1. 输入错误处理
- **格式错误**：实时提示正确格式
- **必填错误**：清晰标识必填字段
- **长度错误**：显示字符长度限制
- **重复错误**：避免重复错误提示

### 2. 网络错误处理
- **请求超时**：显示超时重试提示
- **网络断开**：显示网络连接异常
- **服务器错误**：显示服务异常，请稍后重试
- **权限错误**：显示无权限操作提示

### 3. 业务错误处理
- **预约状态错误**：预约状态不符合创建条件
- **重复创建错误**：该预约已创建环检单
- **数据不存在**：预约信息不存在或已删除
- **系统繁忙**：系统繁忙，请稍后重试

## 可访问性设计

### 1. 键盘导航
- **Tab序列**：合理的Tab导航顺序
- **Enter提交**：Enter键确认创建操作
- **ESC取消**：ESC键取消操作关闭弹窗
- **箭头导航**：在表单字段间导航

### 2. 屏幕阅读器支持
- **标签关联**：输入框与标签正确关联
- **错误提示**：错误信息可被屏幕阅读器识别
- **状态变化**：按钮状态变化有语音提示
- **操作确认**：重要操作有语音确认

### 3. 视觉设计
- **颜色对比**：确保足够的颜色对比度
- **字体大小**：最小字体14px保证可读性
- **焦点指示**：清晰的焦点指示器
- **状态指示**：不仅依赖颜色表示状态

## 性能优化

### 1. 加载优化
- **预加载数据**：预约信息提前缓存
- **懒加载**：非关键信息延迟加载
- **缓存策略**：合理使用本地缓存

### 2. 交互优化
- **防抖处理**：输入验证防抖，避免频繁验证
- **节流处理**：按钮点击节流，防止重复提交
- **即时反馈**：操作立即给出视觉反馈

### 3. 内存管理
- **事件清理**：弹窗关闭时清理事件监听
- **数据清理**：及时清理不需要的数据引用
- **组件销毁**：正确销毁组件避免内存泄漏

## 测试验证

### 1. 功能测试
- ✅ 弹窗正确打开和关闭
- ✅ 预约信息正确显示
- ✅ 送修人信息可编辑
- ✅ 表单验证正确工作
- ✅ 环检单创建成功执行
- ✅ 错误情况正确处理

### 2. 兼容性测试
- ✅ 主流浏览器兼容性
- ✅ 不同屏幕尺寸适配
- ✅ 键盘导航功能完整
- ✅ 屏幕阅读器支持

### 3. 性能测试
- ✅ 弹窗打开速度 < 300ms
- ✅ 表单验证响应 < 100ms
- ✅ 创建请求响应 < 3秒
- ✅ 内存占用合理

### 4. 安全测试
- ✅ 输入数据正确过滤
- ✅ XSS攻击防护
- ✅ 权限验证有效
- ✅ 数据传输安全

## 变更记录
- 2024-11-25：初始版本，基于售后预约管理需求分析创建 