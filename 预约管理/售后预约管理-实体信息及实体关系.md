# 售后预约管理-实体信息及实体关系分析文档

## 文档说明
本文档基于售后预约管理需求分析，识别系统中的核心数据实体、分析实体间关系、梳理数据流向，为数据库设计和系统架构提供依据。

## 1. 核心数据实体识别

### 1.1 主实体分析

#### 预约单实体 (Appointment)
**实体描述**：售后预约的核心业务实体，记录客户预约的完整信息
**主键**：预约单号 (appointment_id)
**生命周期**：从创建到服务完成或取消

**核心属性**：
```
预约单号 (appointment_id): String, 唯一标识, 格式: AP+年月日+序号
预约状态 (status): Enum, 未到店/已到店/未履约
创建时间 (created_at): DateTime, 预约创建时间
预约时间 (appointment_time): DateTime, 客户预约的服务时间
预约时间段 (time_slot): String, 具体时间段, 如09:00-10:00
服务类型 (service_type): Enum, 保养/维修
客户描述 (customer_description): Text, 客户填写的服务需求描述
签到时间 (checkin_time): DateTime, 客户到店签到时间
环检单编号 (inspection_id): String, 关联的环检单编号
```

**关联关系**：
- 多对一关联客户信息（一个客户可以有多个预约单）
- 多对一关联车辆信息（一辆车可以有多个预约单）
- 多对一关联门店信息
- 多对一关联服务顾问
- 一对一关联环检单
- 一对多关联保养套餐项目（保养类型）

#### 客户信息实体 (Customer)
**实体描述**：预约相关的客户信息，包含预约下单人和送修人
**主键**：客户ID (customer_id)

**核心属性**：
```
客户ID (customer_id): String, 唯一标识
预约下单人姓名 (reservation_contact_name): String, 预约创建者姓名
预约下单人手机号 (reservation_contact_phone): String, 预约创建者联系方式
送修人姓名 (service_contact_name): String, 实际到店服务的人员姓名
送修人手机号 (service_contact_phone): String, 送修人联系方式
客户类型 (customer_type): Enum, 个人/企业
注册时间 (register_time): DateTime, 客户注册时间
```

**数据特点**：
- 预约下单人与送修人可能为同一人也可能不同
- 送修人信息在创建环检单时可编辑
- 手机号为关键搜索和验证字段

#### 车辆信息实体 (Vehicle)
**实体描述**：预约服务的车辆详细信息
**主键**：VIN码 (vin)

**核心属性**：
```
VIN码 (vin): String, 17位车辆识别代码, 全球唯一
车牌号 (license_plate): String, 车辆法定标识号码
车型 (model): String, 车辆品牌型号, 如BMW X5
配置 (configuration): String, 车辆具体配置, 如2.0T豪华版
颜色 (color): String, 车辆颜色
里程数 (mileage): Integer, 当前里程数, 单位KM
车龄 (vehicle_age): Integer, 车辆使用时间, 单位月, 由DMS根据车辆交付日期计算得出
注册日期 (register_date): Date, 车辆注册日期
```

**业务用途**：
- 车牌号为客户识别首要信息
- VIN码用于精确车辆识别和历史追溯
- 里程数和车龄用于保养建议和服务匹配

#### 门店信息实体 (Store)
**实体描述**：提供售后服务的门店基本信息
**主键**：门店ID (store_id)

**核心属性**：
```
门店ID (store_id): String, 门店唯一标识
门店名称 (store_name): String, 门店名称
门店地址 (store_address): String, 门店详细地址
门店电话 (store_phone): String, 门店联系电话
门店状态 (store_status): Enum, 正常营业/暂停服务/维护中
营业时间 (business_hours): String, 门店营业时间
二维码内容 (qr_code): String, 门店签到二维码内容
```

**数据隔离特性**：
- 用户只能访问所属门店的数据
- 预约数据按门店进行隔离存储或查询

#### 服务顾问实体 (ServiceAdvisor)
**实体描述**：负责售后服务的门店员工信息
**主键**：服务顾问ID (advisor_id)

**核心属性**：
```
服务顾问ID (advisor_id): String, 唯一标识
姓名 (name): String, 服务顾问姓名
工号 (employee_id): String, 员工工号
手机号 (phone): String, 联系电话
所属门店 (store_id): String, 关联门店ID
职位级别 (level): Enum, 初级/中级/高级
在职状态 (employment_status): Enum, 在职/离职/休假
```

**业务角色**：
- 预约查看和管理
- 环检单创建
- 客户接待和服务安排

#### 环检单实体 (QualityInspection)
**实体描述**：车辆进店后的质量检测记录
**主键**：环检单编号 (inspection_id)

**核心属性**：
```
环检单编号 (inspection_id): String, 唯一标识
关联预约单号 (appointment_id): String, 源预约单
创建时间 (created_at): DateTime, 环检单创建时间
检测状态 (inspection_status): Enum, 待检测/检测中/检测完成
分配服务顾问 (assigned_advisor): String, 分配的服务顾问ID
检测结果 (inspection_result): Text, 检测结果详情
```

**业务关系**：
- 基于已到店预约创建
- 与预约单一对一关系
- 触发正式服务流程启动

### 1.2 辅助实体分析

#### 服务包实体 (ServicePackage)
**实体描述**：预定义的售后服务组合，也称为保养套餐
**主键**：服务包ID (package_id)

**核心属性**：
```
服务包ID (package_id): String, 套餐唯一标识
服务包编码 (package_code): String, 套餐业务代码
服务包名称 (package_name): String, 套餐显示名称
服务包描述 (description): Text, 服务包详细描述
适用车型 (applicable_models): String, 适用车型范围
适用车龄范围 (applicable_age_range): String, 如 12-24月
适用里程范围 (applicable_mileage_range): String, 如 10000-20000KM
标准工时 (standard_hours): Decimal, 标准服务工时
套餐总价 (total_price): Decimal, 服务包总价格
套餐状态 (package_status): Enum, 启用/停用
生效日期 (start_date): Date, 服务包生效日期
失效日期 (end_date): Date, 服务包失效日期
```

#### 服务包项目实体 (ServicePackageItem)
**实体描述**：服务包包含的具体服务项目，包括工时和零配件
**主键**：项目ID (item_id)

**核心属性**：
```
项目ID (item_id): String, 项目唯一标识
服务包ID (package_id): String, 所属服务包
项目类型 (item_type): Enum, 工时(Labor)/零件(Parts)
项目编码 (item_code): String, 工时或零件的业务编码
项目名称 (item_name): String, 具体工时或零配件名称
数量 (quantity): Decimal, 服务数量 (例如工时为1.5, 零件为5)
单位 (unit): String, 数量的单位 (例如 h, 个, L)
单价 (unit_price): Decimal, 项目单价
```

#### 支付信息实体 (Payment)
**实体描述**：服务包的支付相关信息
**主键**：支付ID (payment_id)

**核心属性**：
```
支付ID (payment_id): String, 支付唯一标识
预约单号 (appointment_id): String, 关联预约单
支付状态 (payment_status): Enum, 已支付/未支付/已退款
支付金额 (payment_amount): Decimal, 支付金额
支付方式 (payment_method): Enum, 线上支付/到店支付
支付流水号 (payment_order_number): String, 第三方支付流水号
支付时间 (payment_time): DateTime, 支付完成时间
```

## 2. 实体关系模型

### 2.1 核心实体关系图

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│     客户信息      │────→│     预约单       │←────│     车辆信息      │
│   Customer      │ 1:N │  Appointment    │ N:1 │    Vehicle      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │ 1:1
                               ↓
                        ┌─────────────────┐
                        │     环检单       │
                        │QualityInspection│
                        └─────────────────┘
                               ↑ 1:1
                        ┌─────────────────┐
                        │    服务顾问      │
                        │ ServiceAdvisor  │
                        └─────────────────┘
                               ↑ N:1
                        ┌─────────────────┐
                        │     门店信息      │
                        │     Store       │
                        └─────────────────┘
```

### 2.2 服务包与支付关系图

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│     预约单       │────→│     服务包       │←────│    服务包项目    │
│  Appointment    │ 1:1 │ ServicePackage  │ 1:N │ ServicePackageItem │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │ 1:1
         ↓
┌─────────────────┐
│     支付信息      │
│    Payment      │
└─────────────────┘
```

### 2.3 数据关系约束

#### 主键约束
- 每个实体必须有唯一主键
- 预约单号格式：AP+年月日+序号（AP202411250001）
- VIN码：17位标准格式
- 手机号：11位数字格式

#### 外键约束
- 预约单.客户ID → 客户信息.客户ID (多对一关系)
- 预约单.VIN码 → 车辆信息.VIN码 (多对一关系)
- 预约单.门店ID → 门店信息.门店ID (多对一关系)
- 环检单.预约单号 → 预约单.预约单号 (一对一关系)
- 服务顾问.门店ID → 门店信息.门店ID (多对一关系)

#### 业务约束
- 一个预约单只能创建一个环检单
- 服务顾问分配后不能随意更改
- 门店数据必须隔离，用户只能访问所属门店数据
- 预约状态转换遵循规定流程：未到店→已到店→完成服务

## 3. 数据流分析

### 3.1 预约创建数据流

```
APP端客户操作
    ↓ (HTTP API)
[预约信息] → DMS接收 → [验证数据] → [创建预约单] → [返回预约单号]
    ↓
[预约单实体] ← [客户信息实体] ← [车辆信息实体] ← [门店信息实体]
```

**数据流详情**：
1. **输入数据**：客户信息、车辆信息、服务类型、预约时间、门店选择
2. **处理过程**：数据验证、实体关联、状态初始化
3. **输出数据**：预约单记录、预约单号、创建确认

### 3.2 客户签到数据流

```
APP端扫码操作
    ↓ (实时推送)
[签到信息] → DMS接收 → [验证预约] → [更新状态] → [同步页面]
    ↓
[预约单.status] = "已到店"
[预约单.checkin_time] = 当前时间
```

**数据流详情**：
1. **输入数据**：门店二维码信息、客户身份验证、签到时间
2. **处理过程**：预约验证、状态更新、数据同步
3. **输出数据**：更新的预约状态、签到确认

### 3.3 环检单创建数据流

```
服务顾问操作
    ↓ (DMS内部)
[选择预约单] → [确认信息] → [创建环检单] → [分配服务顾问] → [更新预约状态]
    ↓
[环检单实体] ← [预约单实体] ← [服务顾问实体]
```

**数据流详情**：
1. **输入数据**：预约单信息、送修人信息（可编辑）、操作人员   
2. **处理过程**：信息确认、环检单创建、服务顾问分配
3. **输出数据**：环检单记录、更新的预约状态





