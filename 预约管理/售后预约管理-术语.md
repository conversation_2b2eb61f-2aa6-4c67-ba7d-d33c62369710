uu# 售后预约管理-术语说明文档

## 文档说明
本文档基于售后预约管理需求分析，提取并定义了系统中使用的核心术语，确保术语在整个项目中的一致性和准确性。

## 核心业务术语

### 1. 预约管理相关术语

#### 售后预约 (After-Sales Appointment)
**定义**：客户通过APP或其他渠道预定售后服务（保养、维修）的时间和服务内容的业务行为。
**英文**：After-Sales Appointment
**使用场景**：贯穿整个售后服务流程
**属性**：包含预约单号、客户信息、车辆信息、服务类型、预约时间等

#### 预约单 (Appointment Order)
**定义**：记录客户预约信息的业务单据，是售后预约的数据载体。
**英文**：Appointment Order
**别名**：预约记录
**关键属性**：预约单号、预约状态、创建时间、预约时间、服务类型
**生命周期**：从创建到履约完成或取消

#### 预约单号 (Appointment Number)
**定义**：预约单的唯一标识符，用于系统内部管理和客户查询。
**英文**：Appointment Number
**格式规范**：AP + 年月日 + 序号（如：AP202411250001）
**特点**：唯一性、可读性、可追溯性

### 2. 预约状态相关术语

#### 未到店 (Not Arrived)
**定义**：客户已完成预约但尚未到店签到的状态。
**英文**：Not Arrived
**别名**：待到店
**业务含义**：预约有效，等待客户到店
**后续状态**：已到店、未履约

#### 已到店 (Arrived)
**定义**：客户已到店并完成APP扫码签到确认的状态。
**英文**：Arrived
**触发条件**：客户使用APP扫描门店二维码完成签到
**业务含义**：客户已到店，可以进行后续服务
**后续操作**：创建环检单、安排服务

#### 未履约 (No Show)
**定义**：客户在预约时间段内未到店签到的状态。
**英文**：No Show
**触发条件**：预约时间过期且客户未签到
**业务含义**：预约失效，可能需要客户重新预约

### 3. 用户角色相关术语

#### 服务顾问 (Service Advisor)
**定义**：负责售后服务流程管理、客户沟通和服务安排的门店工作人员。
**英文**：Service Advisor
**主要职责**：监控预约、查看详情、为到店客户创建环检单、安排后续服务。
**权限范围**：查看预约、创建质检单、更新预约状态

#### 门店经理 (Store Manager)
**定义**：负责门店整体运营管理和业务监控的管理人员。
**英文**：Store Manager
**主要职责**：监控门店整体预约数据、分析到店率等核心指标、进行资源调配和业务分析。
**权限范围**：完整预约查看权限、数据导出、统计分析

#### 预约下单人 (Reservation Contact)
**定义**：在APP或其他渠道创建预约的人员。
**英文**：Reservation Contact
**说明**：可能与送修人为同一人，也可能是代为预约的其他人员
**关键信息**：姓名、手机号

#### 送修人 (Service Contact)
**定义**：实际到店接受服务的人员。
**英文**：Service Contact
**说明**：实际享受服务的客户，可能与预约下单人不同
**关键信息**：姓名、手机号、身份验证信息

### 4. 服务类型相关术语

#### 保养 (Maintenance)
**定义**：按照厂家规范对车辆进行定期保养的服务类型。
**英文**：Maintenance
**特点**：通常基于标准化的服务包，客户可选择线上支付或到店支付。
**包含内容**：服务包、工时(Labor)信息、零件(Parts)清单

#### 服务包 (Service Package)
**定义**：针对不同车型、车龄、里程设计的标准化保养服务组合。
**英文**：Service Package
**别名**：保养套餐
**属性**：服务包编码、名称、适用规则、包含项目、总价。
**用途**：简化客户选择、标准化服务流程、支持线上预售。

#### 线上支付 (Online Payment)
**定义**：客户在预约保养服务包时，通过线上渠道预先支付费用的方式。
**英文**：Online Payment
**业务含义**：锁定服务价格，简化到店支付流程。

#### 到店支付 (Pay at Store)
**定义**：客户在完成服务后，在门店内进行费用结算的支付方式。
**英文**：Pay at Store
**业务含义**：适用于维修和未线上预付的保养服务。

#### 维修 (Repair)
**定义**：针对车辆故障或客户需求进行的非标准化修复服务。
**英文**：Repair
**特点**：个性化需求、现场诊断、按实际情况计费
**包含内容**：客户描述、故障诊断、维修方案

### 5. 车辆信息相关术语

#### 车牌号 (License Plate)
**定义**：车辆的法定标识号码。
**英文**：License Plate
**格式**：按各地区规范（如：京A12345）
**用途**：客户识别的首要信息、搜索关键字

#### VIN码 (Vehicle Identification Number)
**定义**：车辆识别代码，全球通用的车辆唯一标识。
**英文**：VIN / Vehicle Identification Number
**格式**：17位字符组合
**特点**：全球唯一、包含车辆制造信息
**用途**：车辆精确识别、维修历史追溯

#### 车型 (Model)
**定义**：车辆的品牌型号信息。
**英文**：Model
**示例**：BMW X5、奔驰C200等
**用途**：服务方案确定、配件匹配

#### 配置 (ver)
**定义**：车辆的具体配置规格信息。
**英文**：Configuration
**示例**：2.0T豪华版、3.0T运动版等
**用途**：精确服务匹配、配件选择

#### 里程数 (Mileage)
**定义**：车辆已行驶的总里程数。
**英文**：Mileage
**单位**：公里（KM）
**用途**：保养周期判断、服务建议

#### 车龄 (Vehicle Age)
**定义**：车辆从注册到当前的使用时间，由DMS根据车辆交付日期计算得出。
**英文**：Vehicle Age
**单位**：月（M）或年（Y）
**用途**：保养策略制定、服务建议

### 6. 技术系统相关术语

#### DMS系统 (Dealer Management System)
**定义**：经销商管理系统，管理销售和售后业务的核心系统。
**英文**：Dealer Management System
**简称**：DMS
**功能范围**：预约管理、客户管理、库存管理、财务管理等

#### APP扫码签到 (APP QR Code Check-in)
**定义**：客户使用手机APP扫描门店二维码完成到店确认的功能。
**英文**：APP QR Code Check-in
**流程**：扫码 → 身份验证 → 状态更新 → 确认完成
**优势**：无接触、自动化、实时同步

#### 环检单 (Quality Inspection Sheet)
**定义**：车辆进店后的质量检测记录单据。
**英文**：Quality Inspection Sheet
**别名**：质检单、检测单
**作用**：记录车辆状态、制定服务方案、责任界定
**关联**：基于预约单创建，一对一关系

#### 到店率 (Arrival Rate)
**定义**：已到店预约数量占总预约数量的百分比。
**英文**：Arrival Rate
**计算公式**：已到店数 ÷ 总预约数 × 100%
**用途**：业务效率评估、运营质量监控

### 7. 系统功能相关术语

#### 预约看板 (Appointment Dashboard)
**定义**：展示预约概览统计和关键指标的可视化界面。
**英文**：Appointment Dashboard
**功能**：数据概览、趋势分析、快速筛选
**用户**：主要面向门店经理和服务顾问

#### 实时同步 (Real-time Synchronization)
**定义**：系统间数据变更的即时传递和更新机制。
**英文**：Real-time Synchronization
**场景**：APP签到 → DMS状态更新
**要求**：5秒内完成状态同步

#### 数据脱敏 (Data Masking)
**定义**：对敏感信息进行隐藏或替换处理的安全措施。
**英文**：Data Masking
**应用场景**：数据导出、日志记录、报表展示
**示例**：138****1234（手机号脱敏）

#### 门店数据隔离 (Store Data Isolation)
**定义**：确保各门店只能访问自己门店数据的权限控制机制。
**英文**：Store Data Isolation
**原则**：数据访问边界清晰、跨店访问禁止
**实现**：基于用户门店归属进行数据过滤

## 术语关系图

```
售后预约管理
├── 预约单管理
│   ├── 预约单（核心实体）
│   ├── 预约状态（未到店/已到店/未履约）
│   └── 预约单号（唯一标识）
├── 用户角色
│   ├── 服务顾问（主要操作者）
│   ├── 门店经理（监控管理者）
│   ├── 预约下单人（预约创建者）
│   └── 送修人（服务接受者）
├── 服务类型
│   ├── 保养（标准化服务）
│   │   ├── 服务包
│   │   └── 支付方式（线上预付/到店支付）
│   └── 维修（个性化服务）
├── 车辆信息
│   ├── 车牌号（主要标识）
│   ├── VIN码（唯一标识）
│   ├── 车型/配置（服务匹配）
│   └── 里程/车龄（服务依据）
└── 系统功能
    ├── DMS系统（核心平台）
    ├── APP扫码签到（状态触发）
    ├── 环检单（后续流程）
    └── 数据安全（权限控制）
```

## 术语使用规范

### 1. 优先级规范
- **必须使用**：预约单、预约单号、服务顾问、门店经理
- **推荐使用**：售后预约、预约状态、服务包、环检单
- **可选使用**：预约看板、数据脱敏、实时同步

### 2. 一致性要求
- 同一概念在系统中必须使用统一术语
- 避免使用别名，如有必要需明确标注
- 术语翻译保持中英文对照一致性

### 3. 扩展性原则
- 新增术语需遵循现有命名规范
- 复合术语优先使用已定义的基础术语组合
- 术语定义需考虑未来业务扩展需求

## 变更记录
- 2024-11-25：初始版本，基于售后预约管理需求分析创建 